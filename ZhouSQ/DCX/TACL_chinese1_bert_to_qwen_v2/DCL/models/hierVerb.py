import openprompt
from openprompt import PromptForClassification
from openprompt.prompt_base import Template, Verbalizer
import torch
import os
from typing import List
from transformers.utils.dummy_pt_objects import PreTrainedModel
from tqdm import tqdm
from transformers import Bert<PERSON>oken<PERSON>, AutoTokenizer
from util.utils import _mask_tokens
from util.eval import compute_score, compute_based_on_path
from models.loss import constraint_multi_depth_loss_func, flat_contrastive_loss_func,sim, adaptive_loss_scaling
import pickle
import random
import json

class HierVerbPromptForClassification(PromptForClassification):
    def __init__(self,
                 plm: PreTrainedModel,
                 template: Template,
                 verbalizer_list: List[Verbalizer],
                 tokenizer,
                 freeze_plm: bool = False,
                 plm_eval_mode: bool = False,
                 verbalizer_mode=False,
                 args=None,
                 processor=None,
                 logger=None,
                 use_cuda=True,
                 
                 ):
        super().__init__(plm=plm, template=template, verbalizer=verbalizer_list[0], freeze_plm=freeze_plm,
                         plm_eval_mode=plm_eval_mode)
        self.verbalizer_list = verbalizer_list
        self.verbLength = len(self.verbalizer_list)
        self.verbalizer_mode = verbalizer_mode
        self._tokenizer = tokenizer

        for idx, verbalizer in enumerate(self.verbalizer_list):
            self.__setattr__(f"verbalizer{idx}", verbalizer)
        self.args = args
        self.processor = processor
        self.use_cuda = use_cuda
        self.logger = logger
        if self.args.mean_verbalizer:
            self.init_embeddings()
        self.flag_constraint_loss = False
        self.flag_contrastive_loss = False
        self.flag_contrastive_logits = False

        # 初始化标签相似度矩阵（用于DCL）
        self.label_sim = None
        # 如果启用了对比损失，需要初始化相似度矩阵
        if hasattr(args, 'contrastive_loss') and args.contrastive_loss:
            self._initialize_label_similarity(args)
    
    def _initialize_label_similarity(self, args):
        """初始化标签相似度矩阵"""
        try:
            # 如果启用了标签描述，尝试加载预计算的相似度矩阵
            if hasattr(args, 'use_label_description') and args.use_label_description:
                similarity_path = f"./label_descriptions/{args.dataset}_similarity.pt"
                if os.path.exists(similarity_path):
                    self.label_sim = torch.load(similarity_path)
                    self.print_info(f"Loaded label similarity matrix from {similarity_path}")
                    return

            # 如果没有预计算的相似度矩阵，创建基于标签层次结构的相似度矩阵
            self.print_info("Creating hierarchical label similarity matrix")
            num_labels = len(self.processor.label_list[-1])  # 叶节点数量

            # 创建基于层次结构的相似度矩阵
            similarity_matrix = torch.zeros(num_labels, num_labels)

            # 对角线设为1（自相似）
            similarity_matrix.fill_diagonal_(1.0)

            # 基于层次结构计算相似度
            for i in range(num_labels):
                for j in range(i+1, num_labels):
                    # 计算两个标签的层次路径相似度
                    sim_score = self._compute_hierarchical_similarity(i, j)
                    similarity_matrix[i, j] = sim_score
                    similarity_matrix[j, i] = sim_score  # 对称矩阵

            self.label_sim = similarity_matrix
            self.print_info(f"Created hierarchical similarity matrix: {similarity_matrix.shape}")

        except Exception as e:
            self.print_info(f"Error initializing label similarity: {e}")
            # 创建单位矩阵作为最后的备选
            num_labels = len(self.processor.label_list[-1])
            self.label_sim = torch.eye(num_labels)
            self.print_info(f"Using identity matrix as fallback: {self.label_sim.shape}")

    def _compute_hierarchical_similarity(self, label1_idx, label2_idx):
        """
        基于层次结构计算两个叶节点标签的相似度
        """
        try:
            # 获取两个标签的层次路径
            path1 = self._get_label_path(label1_idx)
            path2 = self._get_label_path(label2_idx)

            # 计算路径的公共前缀长度
            common_depth = 0
            min_depth = min(len(path1), len(path2))

            for i in range(min_depth):
                if path1[i] == path2[i]:
                    common_depth += 1
                else:
                    break

            # 相似度 = 公共深度 / 最大深度
            max_depth = max(len(path1), len(path2))
            similarity = common_depth / max_depth if max_depth > 0 else 0.0

            return similarity

        except Exception as e:
            # 如果计算失败，返回较小的相似度
            return 0.1

    def _get_label_path(self, leaf_label_idx):
        """
        获取叶节点标签的完整层次路径
        """
        path = [leaf_label_idx]  # 从叶节点开始
        current_idx = leaf_label_idx

        # 从叶节点向上追溯到根节点
        for depth in range(len(self.processor.label_list) - 2, -1, -1):
            if depth in self.processor.hier_mapping and current_idx in self.processor.hier_mapping[depth][1]:
                parent_idx = self.processor.hier_mapping[depth][1][current_idx]
                path.insert(0, parent_idx)  # 在前面插入父节点
                current_idx = parent_idx
            else:
                break

        return path

    def load_label_sim(self, path):
        """保持向后兼容的标签相似度加载函数"""
        try:
            label_des = json.load(open(path))

            # 动态获取模型的hidden size而不是硬编码768
            if hasattr(self.plm.config, 'hidden_size'):
                hidden_size = self.plm.config.hidden_size
            elif hasattr(self.plm.config, 'd_model'):
                hidden_size = self.plm.config.d_model
            else:
                # 默认值，但会打印警告
                hidden_size = 768
                self.print_info(f"Warning: Cannot determine hidden size, using default {hidden_size}")

            emb = torch.zeros(len(label_des), hidden_size)
            for i, l in enumerate(label_des):
                # 使用模型计算embedding
                with torch.no_grad():
                    inputs = self.tokenizer(l, max_length=512, add_special_tokens=True,
                                          truncation=True, padding=True, return_tensors="pt")
                    if hasattr(self.plm, 'get_input_embeddings'):
                        # 对于BERT类模型
                        outputs = self.plm(**inputs, output_hidden_states=True)
                        emb[i] = outputs.hidden_states[-1].mean(dim=1).squeeze()
                    else:
                        # 对于其他模型，使用简单的embedding平均
                        input_embeds = self.plm.get_input_embeddings()(inputs['input_ids'])
                        emb[i] = input_embeds.mean(dim=1).squeeze()

            return torch.cdist(emb, emb)
        except Exception as e:
            self.print_info(f"Error in load_label_sim: {e}")
            return None


    def forward(self, batch) -> torch.Tensor:
        r"""
        Get the logits of label words.

        Args:
            batch (:obj:`Union[Dict, InputFeatures]`): The original batch

        Returns:
            :obj:`torch.Tensor`: The logits of the lable words (obtained by the current verbalizer).
        """

        # debug
        loss = 0
        loss_details = [0, 0, 0, 0]
        lm_loss = None
        constraint_loss = None
        contrastive_loss = None
        args = self.args
        if args.use_dropout_sim and self.training:
            if not self.flag_contrastive_logits:
                print("using contrastive_logits")
                self.flag_contrastive_logits = True
            contrastive_batch = dict()
            for k, v in batch.items():
                tmp = []
                for i in v:
                    tmp.append(i)
                    tmp.append(i)
                contrastive_batch[k] = torch.stack(tmp) if isinstance(tmp[0], torch.Tensor) else tmp
                contrastive_batch[k] = contrastive_batch[k].to("cuda:0")
            batch = contrastive_batch

        outputs = self.prompt_model(batch)
        outputs = self.verbalizer_list[0].gather_outputs(outputs)
        # outputs = self.verbalizer1.gather_outputs(outputs)

        if isinstance(outputs, tuple):
            outputs_at_mask = [self.extract_at_mask(output, batch) for output in outputs]
        else:
            outputs_at_mask = self.extract_at_mask(outputs, batch)

        # 调试输出（可选）
        # if self.training:
        #     print(f"Debug: outputs_at_mask shape: {outputs_at_mask.shape}")
        #     print(f"Debug: verbLength: {self.verbLength}")

        logits = []
        for idx in range(self.verbLength):
            # 检查outputs_at_mask的维度
            if len(outputs_at_mask.shape) == 3:
                # 期望的形状: [batch_size, num_masks, hidden_size]
                if idx < outputs_at_mask.shape[1]:
                    mask_output = outputs_at_mask[:, idx, :]
                else:
                    # 如果索引超出范围，使用最后一个mask的输出
                    mask_output = outputs_at_mask[:, -1, :]
            elif len(outputs_at_mask.shape) == 2:
                # 如果只有2维，说明只有一个mask，所有verbalizer使用相同的输出
                mask_output = outputs_at_mask
            else:
                raise ValueError(f"Unexpected outputs_at_mask shape: {outputs_at_mask.shape}")

            label_words_logtis = self.__getattr__(f"verbalizer{idx}").process_outputs(mask_output, batch=batch)
            logits.append(label_words_logtis)

        if self.training:

            labels = batch['label']

            hier_labels = []
            hier_labels.insert(0, labels)
            for idx in range(args.depth - 2, -1, -1):
                cur_depth_labels = torch.zeros_like(labels)
                for i in range(len(labels)):
                    # cur_depth_labels[i] = label1_to_label0_mapping[labels[i].tolist()]
                    # hierVerb.py 修改建议
                    # label_index = hier_labels[0][i].tolist()
                    # if label_index in self.processor.hier_mapping[idx][1]:
                    #     cur_depth_labels[i] = self.processor.hier_mapping[idx][1][label_index]
                    # else:
                    #     # 处理无效索引：记录警告/使用默认值
                    #     cur_depth_labels[i] = 0  
                    cur_depth_labels[i] = self.processor.hier_mapping[idx][1][hier_labels[0][i].tolist()]
                hier_labels.insert(0, cur_depth_labels)

            ## Language Model loss (MLM for BERT, CLM for Qwen3)
            if args.lm_training:
                input_ids = batch['input_ids']

                # 检查是否为Qwen3模型（生成式模型）
                if hasattr(self.plm.config, 'model_type') and 'qwen' in self.plm.config.model_type.lower():
                    # Qwen3使用因果语言模型损失（CLM）
                    # 将input_ids向右移动一位作为labels
                    labels = input_ids.clone()
                    labels[:, :-1] = input_ids[:, 1:]  # 向左移动labels
                    labels[:, -1] = -100  # 最后一个token不计算损失

                    lm_inputs = {
                        "input_ids": input_ids,
                        "attention_mask": batch['attention_mask'],
                        "labels": labels
                    }
                else:
                    # BERT使用掩码语言模型损失（MLM）
                    input_ids, labels = _mask_tokens(self.tokenizer, input_ids.cpu())
                    lm_inputs = {
                        "input_ids": input_ids,
                        "attention_mask": batch['attention_mask'],
                        "labels": labels
                    }

                for k, v in lm_inputs.items():
                    if v is not None:
                        lm_inputs[k] = v.to(self.device)

                # 获取语言模型损失
                model_outputs = self.plm(**lm_inputs)
                if hasattr(model_outputs, 'loss'):
                    lm_loss = model_outputs.loss
                else:
                    lm_loss = model_outputs[0]

            if args.multi_label:
                loss_func = torch.nn.BCEWithLogitsLoss()
            else:
                loss_func = torch.nn.CrossEntropyLoss()

            for idx, cur_depth_label in enumerate(hier_labels):
                cur_depth_logits = logits[idx]
                if args.multi_label:
                    cur_multi_label = torch.zeros_like(cur_depth_logits)

                    for i in range(cur_multi_label.shape[0]):
                        cur_multi_label[i][cur_depth_label[i]] = 1
                    cur_depth_label = cur_multi_label
                depth_loss = loss_func(cur_depth_logits, cur_depth_label)

                # 应用自适应损失缩放（针对Qwen模型）
                if hasattr(self.plm, 'config') and hasattr(self.plm.config, 'model_type'):
                    model_name = self.plm.config.model_type
                    vocab_size = getattr(self.plm.config, 'vocab_size', None)
                    depth_loss = adaptive_loss_scaling(depth_loss, model_name, vocab_size)

                loss += depth_loss

            loss_details[0] += loss.item()  # 层级二loss

            ## hierarchical constraint chain
            if args.constraint_loss:
                if not self.flag_constraint_loss:
                    print(f"using constraint loss with alpha {args.constraint_alpha}")
                    self.flag_constraint_loss = True
                constraint_loss = constraint_multi_depth_loss_func(logits, loss_func, hier_labels, self.processor, args,
                                                                   use_cuda=self.use_cuda, mode=0)

                # 对constraint loss进行缩放，防止数值过大
                if hasattr(self.plm, 'config') and hasattr(self.plm.config, 'model_type'):
                    model_name = self.plm.config.model_type
                    constraint_loss = adaptive_loss_scaling(constraint_loss, model_name)
                    # 额外缩放因子，因为constraint loss通常比较大
                    constraint_loss = constraint_loss * 0.1

            if args.contrastive_loss:
                if not self.flag_contrastive_loss:
                    print(f"using DCL loss with alpha {args.contrastive_alpha}")
                    if args.use_dropout_sim:
                        print("using use_dropout_sim")
                    self.flag_contrastive_loss = True
                # 从batch中提取input_ids作为文本内容的代理
                text_contents = None
                if 'input_ids' in batch:
                    # 将input_ids转换为字符串用于比较文本相似性
                    text_contents = [str(ids.tolist()) for ids in batch['input_ids']]

                contrastive_loss = flat_contrastive_loss_func(self.label_sim, hier_labels, self.processor,
                                                                            outputs_at_mask,
                                                                            imbalanced_weight=args.imbalanced_weight,
                                                                            contrastive_level=args.contrastive_level,
                                                                            imbalanced_weight_reverse=args.imbalanced_weight_reverse,
                                                                            depth=args.depth,
                                                                            use_cuda=self.use_cuda,
                                                                            text_contents=text_contents)

            ####
            # cur_batch_size = outputs_at_mask.shape[0]
            # contrastive_loss = 0
            # for idx, cur_depth_label in enumerate(hier_labels):
            #     sim_score = sim(outputs_at_mask[:,idx,:], outputs_at_mask[:,idx,:])
            #     sim_score = torch.exp(sim_score)
            #     cur_hier_matrix = torch.zeros(cur_batch_size, cur_batch_size)
            #     for i in range(len(cur_depth_label)):
            #         for j in range(len(cur_depth_label)):
            #             if cur_depth_label[i] == cur_depth_label[j]:
            #                 cur_hier_matrix[i][j] = 1
            #             else:
            #                 cur_hier_matrix[i][j] = 0
                
            #     pos_sim = sim_score[cur_hier_matrix != 0].sum()
            #     neg_sim = sim_score[cur_hier_matrix == 0].sum()
            #     contrastive_loss += - torch.log(pos_sim / (pos_sim + neg_sim))

            #####
            if lm_loss is not None:
                if args.lm_alpha != -1:
                    loss = loss * args.lm_alpha + (1 - args.lm_alpha) * lm_loss
                else:
                    loss += lm_loss
                loss_details[1] += lm_loss.item()

            if constraint_loss is not None:
                if args.constraint_alpha != -1:
                    # 修改权重组合方式，降低constraint loss的影响
                    loss = loss + args.constraint_alpha * constraint_loss
                else:
                    loss += constraint_loss
                loss_details[2] += constraint_loss.item()

            if contrastive_loss is not None:
                if args.contrastive_alpha != -1:
                    # 修改权重组合方式，增加contrastive loss的影响
                    loss += args.contrastive_alpha * contrastive_loss
                else:
                    loss += contrastive_loss
                loss_details[3] += contrastive_loss.item()

            return logits, loss, loss_details
        else:
            return logits, outputs_at_mask

    def init_embeddings(self):
        self.print_info("using label emb for soft verbalizer")

        label_emb_list = []
        # label_name_list = []
        for idx in range(self.args.depth):

            label_dict = self.processor.label_list[idx]
            label_dict = dict({idx: v for idx, v in enumerate(label_dict)})

            # 处理tokenizer编码，适配不同的tokenizer
            if hasattr(self.tokenizer, 'encode'):
                label_dict = {i: self.tokenizer.encode(v, add_special_tokens=False) for i, v in label_dict.items()}
            else:
                # 备用方法
                label_dict = {i: self.tokenizer(v, add_special_tokens=False)['input_ids'] for i, v in label_dict.items()}

            label_emb = []
            # label_name = []

            # 获取embedding层，适配不同模型架构
            if hasattr(self.plm, 'get_input_embeddings'):
                input_embeds = self.plm.get_input_embeddings()
            elif hasattr(self.plm, 'model') and hasattr(self.plm.model, 'embed_tokens'):
                # Qwen3模型结构
                input_embeds = self.plm.model.embed_tokens
            elif hasattr(self.plm, 'transformer') and hasattr(self.plm.transformer, 'wte'):
                # 其他transformer结构
                input_embeds = self.plm.transformer.wte
            else:
                raise ValueError("Cannot find input embeddings in the model")

            for i in range(len(label_dict)):
                token_ids = label_dict[i]
                if len(token_ids) > 0:
                    # 确保token_ids在正确的设备上
                    token_tensor = torch.tensor(token_ids, device=self.device)
                    label_emb.append(
                        input_embeds.weight.index_select(0, token_tensor).mean(dim=0))
                else:
                    # 如果没有token，使用零向量
                    label_emb.append(torch.zeros(input_embeds.weight.shape[1], device=self.device))
                # label_name.append(label_dict[i])
            label_emb = torch.stack(label_emb)
            label_emb_list.append(label_emb)
            # label_name_list.append(label_name)
        if self.args.use_hier_mean:
            for depth_idx in range(self.args.depth - 2, -1, -1):
                cur_label_emb = label_emb_list[depth_idx]
                # cur_label_name = label_name_list[depth_idx]
                cur_depth_length = len(self.processor.label_list[depth_idx])

                for i in range(cur_depth_length):
                    # 检查映射索引是否有效
                    mapped_indices = self.processor.hier_mapping[depth_idx][0][i]
                    next_level_size = len(label_emb_list[depth_idx + 1])
                    
                    # 确保所有映射索引都在下一层级的范围内
                    if not all(0 <= idx < next_level_size for idx in mapped_indices):
                        self.print_info(f"Warning: Invalid mapping at depth {depth_idx}, index {i} -> {mapped_indices}")
                        continue
                        
                    # 计算有效索引的平均值
                    cur_label_emb[i] += label_emb_list[depth_idx + 1][mapped_indices, :].mean(dim=0)
                label_emb_list[depth_idx] = cur_label_emb
                # label_name_list[depth_idx] = cur_label_name
        for idx in range(self.args.depth):
            label_emb = label_emb_list[idx]
            self.print_info(f"depth {idx}: {label_emb.shape}")

            verbalizer = self.__getattr__(f"verbalizer{idx}")

            # 尝试不同的verbalizer结构
            try:
                # 确保label_emb与模型的数据类型匹配
                model_dtype = next(self.plm.parameters()).dtype
                if label_emb.dtype != model_dtype:
                    label_emb = label_emb.to(dtype=model_dtype)
                    self.print_info(f"Converting label_emb from {label_emb.dtype} to {model_dtype}")

                if "0.1.2" in openprompt.__path__[0]:
                    # 确保head层的数据类型匹配
                    verbalizer.head_last_layer = verbalizer.head_last_layer.to(dtype=model_dtype)
                    verbalizer.head_last_layer.weight.data = label_emb
                    verbalizer.head_last_layer.weight.data.requires_grad = True
                elif hasattr(verbalizer, 'head') and hasattr(verbalizer.head, 'predictions'):
                    # 旧版本结构
                    decoder = getattr(verbalizer.head.predictions, 'decoder')
                    decoder = decoder.to(dtype=model_dtype)
                    decoder.weight.data = label_emb
                    decoder.weight.data.requires_grad = True
                elif hasattr(verbalizer, 'head') and hasattr(verbalizer.head, 'weight'):
                    # 新版本结构 - 直接是Linear层
                    verbalizer.head = verbalizer.head.to(dtype=model_dtype)
                    verbalizer.head.weight.data = label_emb
                    verbalizer.head.weight.data.requires_grad = True
                else:
                    # 尝试查找其他可能的权重属性
                    self.print_info(f"Warning: Unknown verbalizer structure for depth {idx}")
                    self.print_info(f"Verbalizer attributes: {dir(verbalizer)}")
                    if hasattr(verbalizer, 'head'):
                        self.print_info(f"Head attributes: {dir(verbalizer.head)}")
            except Exception as e:
                self.print_info(f"Error setting verbalizer weights for depth {idx}: {e}")
                # 继续处理其他层

    def evaluate(self, dataloader, processor, desc="Valid", mode=0, device="cuda:0", args=None):
        self.eval()
        pred = []
        truth = []
        pbar = tqdm(dataloader, desc=desc)
        hier_mapping = processor.hier_mapping
        depth = len(hier_mapping) + 1
        all_length = len(processor.all_labels)
        for step, batch in enumerate(pbar):
            if hasattr(batch, 'cuda'):
                batch = batch.cuda()
            else:
                batch = tuple(t.to(device) if isinstance(t, torch.Tensor) else t for t in batch)
                batch = {"input_ids": batch[0], "attention_mask": batch[1],
                         "label": batch[2], "loss_ids": batch[3]}
            logits, leaf_embed = self(batch)
            leaf_labels = batch['label']
            hier_labels = []
            hier_labels.insert(0, leaf_labels)
            for idx in range(depth - 2, -1, -1):
                cur_depth_labels = torch.zeros_like(leaf_labels)
                for i in range(len(leaf_labels)):
                    label_index = hier_labels[0][i].tolist()
                    if label_index in self.processor.hier_mapping[idx][1]:
                        cur_depth_labels[i] = self.processor.hier_mapping[idx][1][label_index]
                    else:
                        # 处理无效索引：记录警告/使用默认值
                        cur_depth_labels[i] = 0  
                    # cur_depth_labels[i] = hier_mapping[idx][1][hier_labels[0][i].tolist()]
                hier_labels.insert(0, cur_depth_labels)

            if isinstance(logits, list):
                leaf_logits = logits[-1]
            elif isinstance(logits, torch.Tensor):
                leaf_logits = logits[:, -1, :]
            leaf_logits = torch.softmax(leaf_logits, dim=-1)
            batch_preds = []
            batch_golds = []

            leaf_preds = torch.argmax(leaf_logits, dim=-1).cpu().tolist()
            leaf_labels = leaf_labels.cpu().tolist()

            batch_preds.insert(0, leaf_preds)
            batch_golds.insert(0, leaf_labels)

            batch_s = leaf_logits.shape[0]
            flat_slot2value = processor.flat_slot2value
            hier_logits = []
            hier_logits.insert(0, leaf_logits)

            for depth_idx in range(depth - 2, -1, -1):
                ori_logits = torch.softmax(logits[depth_idx], dim=-1)

                if ori_logits.shape[-1] != all_length:
                    cur_logits = torch.zeros(batch_s, len(processor.label_list[depth_idx]))
                    for i in range(cur_logits.shape[-1]):
                        # 检查映射索引是否存在且有效
                        if depth_idx not in hier_mapping or \
                           i not in hier_mapping[depth_idx][0]:
                            # self.print_info(f"Warning: Missing mapping at depth {depth_idx}, index {i}")
                            continue
                            
                        mapped_indices = hier_mapping[depth_idx][0][i]
                        # 确保所有索引都在hier_logits的范围内
                        valid_indices = [idx for idx in mapped_indices if idx < hier_logits[0].shape[-1]]
                        if not valid_indices:
                            self.print_info(f"Warning: No valid indices at depth {depth_idx}, index {i}")
                            continue
                            
                        cur_logits[:, i] = torch.mean(hier_logits[0][:, valid_indices], dim=-1)
                else:
                    cur_logits = torch.zeros(batch_s, all_length)
                    cd_labels = processor.depth2label[depth_idx]
                    for i in range(all_length):
                        if i in cd_labels:
                            cur_logits[:, i] = torch.sum(hier_logits[0][:, list(flat_slot2value[i])], dim=-1)

                cur_logits = cur_logits.to(device)

                # for i in range(cur_label_size):
                #     cur_logits[:, i] = torch.sum(hier_logits[0][:, cur_mapping[0][i]], dim=-1)
                if mode == 0:
                    softmax_label_logits = ori_logits
                elif mode == 1:
                    softmax_label_logits = torch.softmax(cur_logits, dim=-1)
                elif mode == 2:
                    softmax_label_logits = torch.softmax(cur_logits, dim=-1) + ori_logits
                    softmax_label_logits = torch.softmax(softmax_label_logits, dim=-1)

                cur_preds = torch.argmax(softmax_label_logits, dim=-1).cpu().tolist()
                cur_golds = hier_labels[depth_idx].cpu().tolist()

                hier_logits.insert(0, softmax_label_logits)
                batch_preds.insert(0, cur_preds)
                batch_golds.insert(0, cur_golds)
            batch_preds = torch.tensor(batch_preds).transpose(1, 0).cpu().tolist()
            batch_golds = torch.tensor(batch_golds).transpose(1, 0).cpu().tolist()

            for i in range(batch_s):
                sub_preds = []
                sub_golds = []
                prev_label_size = 0
                for depth_idx in range(depth):

                    if depth_idx == 0:
                        sub_preds.append(batch_preds[i][depth_idx])
                        sub_golds.append(batch_golds[i][depth_idx])

                        continue
                    prev_mapping = hier_mapping[depth_idx - 1]
                    prev_label_size = len(prev_mapping[0]) + prev_label_size
                    if leaf_logits.shape[-1] == all_length:
                        sub_preds.append(batch_preds[i][depth_idx])
                    else:
                        sub_preds.append(batch_preds[i][depth_idx] + prev_label_size)
                    sub_golds.append(batch_golds[i][depth_idx] + prev_label_size)
                pred.append(sub_preds)
                truth.append(sub_golds)

        label_dict = dict({idx: label for idx, label in enumerate(processor.all_labels)})      
        if args is None:
            scores = compute_score(pred, truth, label_dict)
        else:
            scores = compute_based_on_path(pred, truth, label_dict, processor, args)
        return scores

    def print_info(self, info):
        if self.logger is not None:
            self.logger.info(info)
        else:
            print(info)

    def state_dict(self, *args, **kwargs):
        """ Save the model using template, plm and verbalizer's save methods."""
        _state_dict = {}
        if not self.prompt_model.freeze_plm:
            _state_dict['plm'] = self.plm.state_dict(*args, **kwargs)
        _state_dict['template'] = self.template.state_dict(*args, **kwargs)
        for idx in range(self.verbLength):
            _state_dict[f'verbalizer{idx}'] = self.__getattr__(f"verbalizer{idx}").state_dict(*args, **kwargs)
        return _state_dict

    def load_state_dict(self, state_dict, *args, **kwargs):
        """ Load the model using template, plm and verbalizer's load methods."""
        if 'plm' in state_dict and not self.prompt_model.freeze_plm:
            self.plm.load_state_dict(state_dict['plm'], *args, **kwargs)
        self.template.load_state_dict(state_dict['template'], *args, **kwargs)

        for idx in range(self.verbLength):
            self.__getattr__(f"verbalizer{idx}").load_state_dict(state_dict[f'verbalizer{idx}'], *args, **kwargs)

    # def create_dcl_inputs(
    #     self,
    #     samples,
    #     tensorizer,
    #     insert_title: bool,
    #     num_hard_negatives: int = 0,
    #     num_other_negatives: int = 0,
    #     shuffle: bool = True,
    #     shuffle_positives: bool = False,
    #     hard_neg_fallback: bool = True,
    #     query_token: str = None,
    # ):
    #     """
    #     Creates a batch of the biencoder training tuple.
    #     :param samples: list of BiEncoderSample-s to create the batch for
    #     :param tensorizer: components to create model input tensors from a text sequence
    #     :param insert_title: enables title insertion at the beginning of the context sequences
    #     :param num_hard_negatives: amount of hard negatives per question (taken from samples' pools)
    #     :param num_other_negatives: amount of other negatives per question (taken from samples' pools)
    #     :param shuffle: shuffles negative passages pools
    #     :param shuffle_positives: shuffles positive passages pools
    #     :return: BiEncoderBatch tuple
    #     """
    #     question_tensors = []
    #     ctx_tensors = []
    #     positive_ctx_indices = []
    #     hard_neg_ctx_indices = []

    #     for sample in samples:
    #         # ctx+ & [ctx-] composition
    #         # as of now, take the first(gold) ctx+ only

    #         if shuffle and shuffle_positives:
    #             positive_ctxs = sample.positive_passages
    #             positive_ctx = positive_ctxs[np.random.choice(len(positive_ctxs))]
    #         else:
    #             positive_ctx = sample.positive_passages[0]

    #         neg_ctxs = sample.negative_passages
    #         hard_neg_ctxs = sample.hard_negative_passages
    #         question = sample.query
    #         # question = normalize_question(sample.query)

    #         if shuffle:
    #             random.shuffle(neg_ctxs)
    #             random.shuffle(hard_neg_ctxs)

    #         if hard_neg_fallback and len(hard_neg_ctxs) == 0:
    #             hard_neg_ctxs = neg_ctxs[0:num_hard_negatives]

    #         neg_ctxs = neg_ctxs[0:num_other_negatives]
    #         hard_neg_ctxs = hard_neg_ctxs[0:num_hard_negatives]

    #         all_ctxs = [positive_ctx] + neg_ctxs + hard_neg_ctxs
    #         hard_negatives_start_idx = 1
    #         hard_negatives_end_idx = 1 + len(hard_neg_ctxs)

    #         current_ctxs_len = len(ctx_tensors)

    #         sample_ctxs_tensors = [
    #             tensorizer.text_to_tensor(
    #                 ctx.text, title=ctx.title if (insert_title and ctx.title) else None
    #             )
    #             for ctx in all_ctxs
    #         ]

    #         ctx_tensors.extend(sample_ctxs_tensors)
    #         positive_ctx_indices.append(current_ctxs_len)
    #         hard_neg_ctx_indices.append(
    #             [
    #                 i
    #                 for i in range(
    #                     current_ctxs_len + hard_negatives_start_idx,
    #                     current_ctxs_len + hard_negatives_end_idx,
    #                 )
    #             ]
    #         )

    #         if query_token:
    #             # TODO: tmp workaround for EL, remove or revise
    #             if query_token == "[START_ENT]":
    #                 query_span = _select_span_with_token(
    #                     question, tensorizer, token_str=query_token
    #                 )
    #                 question_tensors.append(query_span)
    #             else:
    #                 question_tensors.append(
    #                     tensorizer.text_to_tensor(" ".join([query_token, question]))
    #                 )
    #         else:
    #             question_tensors.append(tensorizer.text_to_tensor(question))

    #     ctxs_tensor = torch.cat([ctx.view(1, -1) for ctx in ctx_tensors], dim=0)
    #     questions_tensor = torch.cat([q.view(1, -1) for q in question_tensors], dim=0)

    #     ctx_segments = torch.zeros_like(ctxs_tensor)
    #     question_segments = torch.zeros_like(questions_tensor)

    #     return (
    #         questions_tensor,
    #         question_segments,
    #         ctxs_tensor,
    #         ctx_segments,
    #         positive_ctx_indices,
    #         hard_neg_ctx_indices,
    #         "question",
    #     )

    def get_index_vectors(self, batch):
        """
        获取输入文本的层次化索引向量
        实现论文中的索引向量提取: m1...mc = PLM([P1][P2]...[Pc]x)

        Args:
            batch: 输入批次数据

        Returns:
            torch.Tensor: 索引向量 [batch_size, num_layers, hidden_size]
        """
        with torch.no_grad():
            self.eval()

            # 前向传播获取hidden states
            outputs = self.plm(**batch, output_hidden_states=True)

            # 提取索引向量
            if hasattr(self.template, 'get_soft_embeds'):
                # 软提示模板：提取前C个位置的hidden states
                hidden_states = outputs.last_hidden_state if hasattr(outputs, "last_hidden_state") else outputs[0]
                num_prompt_tokens = self.args.depth

                if hidden_states.shape[1] >= num_prompt_tokens:
                    # 提取软提示位置的hidden states作为索引向量
                    index_vectors = hidden_states[:, :num_prompt_tokens, :]  # [batch_size, C, hidden_size]
                    return index_vectors
                else:
                    # 序列长度不足，使用平均池化
                    pooled = hidden_states.mean(dim=1)  # [batch_size, hidden_size]
                    index_vectors = pooled.unsqueeze(1).expand(-1, num_prompt_tokens, -1)
                    return index_vectors
            else:
                # 硬编码模板：使用mask位置的hidden states
                outputs_at_mask = self.extract_at_mask(outputs, batch)
                return outputs_at_mask

    def extract_index_vector_for_text(self, text):
        """
        为单个文本提取索引向量

        Args:
            text: 输入文本

        Returns:
            torch.Tensor: 索引向量 [num_layers, hidden_size]
        """
        # 编码文本
        inputs = self.tokenizer(
            text,
            return_tensors='pt',
            truncation=True,
            max_length=512,
            padding=True
        )

        # 移动到正确的设备
        if next(self.parameters()).is_cuda:
            inputs = {k: v.cuda() for k, v in inputs.items()}

        # 添加loss_ids（如果需要）
        if 'loss_ids' not in inputs:
            # 创建默认的loss_ids
            seq_len = inputs['input_ids'].shape[1]
            if hasattr(self.template, 'get_soft_embeds'):
                # 软提示模板：前depth个位置
                loss_ids = torch.zeros_like(inputs['input_ids'])
                loss_ids[:, :min(self.args.depth, seq_len)] = 1
            else:
                # 硬编码模板：查找mask位置
                mask_token_id = self.tokenizer.mask_token_id if hasattr(self.tokenizer, 'mask_token_id') else self.tokenizer.unk_token_id
                loss_ids = (inputs['input_ids'] == mask_token_id).long()

            inputs['loss_ids'] = loss_ids

        # 获取索引向量
        with torch.no_grad():
            self.eval()

            # 前向传播获取hidden states
            outputs = self.plm(**inputs, output_hidden_states=True)

            # 提取索引向量
            if hasattr(self.template, 'get_soft_embeds'):
                # 软提示模板：提取前C个位置的hidden states
                hidden_states = outputs.last_hidden_state if hasattr(outputs, "last_hidden_state") else outputs[0]
                num_prompt_tokens = self.args.depth

                if hidden_states.shape[1] >= num_prompt_tokens:
                    # 提取软提示位置的hidden states作为索引向量
                    index_vectors = hidden_states[:, :num_prompt_tokens, :]  # [batch_size, C, hidden_size]
                else:
                    # 序列长度不足，使用平均池化
                    pooled = hidden_states.mean(dim=1)  # [batch_size, hidden_size]
                    index_vectors = pooled.unsqueeze(1).expand(-1, num_prompt_tokens, -1)
            else:
                # 硬编码模板：使用mask位置的hidden states
                outputs_at_mask = self.extract_at_mask(outputs, inputs)
                index_vectors = outputs_at_mask

        return index_vectors.squeeze(0)  # 移除batch维度